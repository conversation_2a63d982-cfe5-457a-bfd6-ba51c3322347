//package com.wohoo_studio.backend.plumwhisperweb.controller;
//
//import com.wohoo_studio.backend.plumwhisperweb.ai.model.dto.AiChatRequestDTO;
//import com.wohoo_studio.backend.plumwhisperweb.ai.model.enums.ModelType;
//import com.wohoo_studio.backend.plumwhisperweb.ai.model.vo.AiChatResponseVO;
//import com.wohoo_studio.backend.plumwhisperweb.ai.service.AiChatService;
//import com.wohoo_studio.backend.plumwhisperweb.common.Result;
//import com.wohoo_studio.backend.plumwhisperweb.vo.AiAnalyzeResVO;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import java.time.LocalDateTime;
//import java.util.List;
//
///**
// * 八字分析控制器
// * <AUTHOR>
// */
//@Slf4j
//@Validated
//@RestController
//@RequestMapping("/bazi")
//public class BaziAnalyzeController {
//
//    @Autowired
//    private AiChatService aiChatService;
//
//    /**
//     * 使用Gemini进行八字分析
//     *
//     * @param userInput 用户输入的八字信息
//     * @return 分析结果
//     */
//    @PostMapping("/analyzeWithGemini")
//    public Result<List<AiAnalyzeResVO>> analyzeWithGemini(@RequestParam String userInput) {
//        try {
//            // 构建AI对话请求
//            AiChatRequestDTO request = new AiChatRequestDTO();
//            request.setModelType(ModelType.GEMINI);
//            request.setUserInput(userInput);
//
//            // 调用AI服务
//            AiChatResponseVO aiResponse = aiChatService.chat(request);
//
//            // 转换为八字分析结果
//            AiAnalyzeResVO result = new AiAnalyzeResVO();
//            result.setContent(aiResponse.getContent());
//            result.setAnalyzeType("gemini_bazi");
//            result.setCreateTime(LocalDateTime.now());
//
//            return Result.success(List.of(result));
//
//        } catch (Exception e) {
//            log.error("八字分析失败", e);
//            return Result.error("八字分析失败: " + e.getMessage());
//        }
//    }
//
//}