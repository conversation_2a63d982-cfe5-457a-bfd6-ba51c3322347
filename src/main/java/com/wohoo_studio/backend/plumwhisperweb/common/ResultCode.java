package com.wohoo_studio.backend.plumwhisperweb.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    SUCCESS(200, "success"),

    FAILED(500, "操作失败"),

    VALIDATE_FAILED(400, "参数检验失败"),

    UNAUTHORIZED(401, "token已过期, 请尝试重新登录"),

    FORBIDDEN(403, "没有相关权限"),

    NOT_FOUND(404, "请求资源不存在"),

    METHOD_NOT_ALLOWED(405, "请求方法不允许"),

    CONFLICT(409, "数据冲突"),

    SERVER_ERROR(500, "服务器内部错误");

    private final Integer code;
    private final String message;

} 