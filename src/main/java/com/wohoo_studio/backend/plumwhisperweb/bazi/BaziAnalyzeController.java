package com.wohoo_studio.backend.plumwhisperweb.bazi;

import com.wohoo_studio.backend.plumwhisperweb.common.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/bazi")
public class BaziAnalyzeController {

    @GetMapping("/analyzeWithGemini")
    public Result<List<AiAnalyzeResVO>> analyzeWithGemini() {
        return Result.success();
    }

}