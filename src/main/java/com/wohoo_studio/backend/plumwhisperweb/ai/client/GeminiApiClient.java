//package com.wohoo_studio.backend.plumwhisperweb.ai.client;
//
//import com.fasterxml.jackson.annotation.JsonProperty;
//import com.wohoo_studio.backend.plumwhisperweb.ai.client.base.BaseApiClient;
//import com.wohoo_studio.backend.plumwhisperweb.ai.config.BaziAnalyzeConfig;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import okhttp3.Request;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * Gemini API客户端
// * <AUTHOR>
// */
//@Slf4j
//@Component
//public class GeminiApiClient extends BaseApiClient {
//
//    @Autowired
//    private BaziAnalyzeConfig baziAnalyzeConfig;
//
//    /**
//     * 调用Gemini API进行对话
//     *
//     * @param apiKey API密钥
//     * @param systemPrompt 系统提示词
//     * @param userInput 用户输入
//     * @param temperature 温度参数
//     * @param maxTokens 最大token数
//     * @return Gemini响应
//     */
//    public GeminiResponse chat(String apiKey, String systemPrompt, String userInput,
//                              Double temperature, Integer maxTokens) {
//        try {
//            String url = String.format("%s/v1beta/models/gemini-1.5-flash-latest:generateContent?key=%s",
//                    baziAnalyzeConfig.getModels().get("gemini").getBaseUrl(), apiKey);
//
//            // 构建请求体
//            GeminiRequest request = new GeminiRequest();
//
//            // 设置内容
//            GeminiContent content = new GeminiContent();
//            content.setRole("user");
//
//            // 如果有系统提示词，先添加系统消息
//            if (systemPrompt != null && !systemPrompt.trim().isEmpty()) {
//                content.setParts(List.of(
//                    new GeminiPart(systemPrompt + "\n\n" + userInput)
//                ));
//            } else {
//                content.setParts(List.of(new GeminiPart(userInput)));
//            }
//
//            request.setContents(List.of(content));
//
//            // 设置生成配置
//            GeminiGenerationConfig config = new GeminiGenerationConfig();
//            config.setTemperature(temperature != null ? temperature :
//                baziAnalyzeConfig.getModels().get("gemini").getDefaultTemperature());
//            config.setMaxOutputTokens(maxTokens != null ? maxTokens :
//                baziAnalyzeConfig.getModels().get("gemini").getDefaultMaxTokens());
//
//            request.setGenerationConfig(config);
//
//            Request httpRequest = new Request.Builder()
//                    .url(url)
//                    .post(createJsonRequestBody(request))
//                    .addHeader("Content-Type", "application/json")
//                    .build();
//
//            String response = executeRequest(httpRequest);
//            GeminiResponse geminiResponse = parseJsonResponse(response, GeminiResponse.class);
//
//            log.info("Gemini API调用成功，使用token: {}",
//                geminiResponse.getUsageMetadata() != null ?
//                geminiResponse.getUsageMetadata().getTotalTokenCount() : "未知");
//
//            return geminiResponse;
//
//        } catch (Exception e) {
//            log.error("调用Gemini API失败", e);
//            throw new RuntimeException("调用Gemini API失败", e);
//        }
//    }
//
//    @Data
//    public static class GeminiRequest {
//        private List<GeminiContent> contents;
//        @JsonProperty("generationConfig")
//        private GeminiGenerationConfig generationConfig;
//    }
//
//    @Data
//    public static class GeminiContent {
//        private String role;
//        private List<GeminiPart> parts;
//    }
//
//    @Data
//    public static class GeminiPart {
//        private String text;
//
//        public GeminiPart() {}
//
//        public GeminiPart(String text) {
//            this.text = text;
//        }
//    }
//
//    @Data
//    public static class GeminiGenerationConfig {
//        private Double temperature;
//        @JsonProperty("maxOutputTokens")
//        private Integer maxOutputTokens;
//    }
//
//    @Data
//    public static class GeminiResponse {
//        private List<GeminiCandidate> candidates;
//        @JsonProperty("usageMetadata")
//        private GeminiUsageMetadata usageMetadata;
//    }
//
//    @Data
//    public static class GeminiCandidate {
//        private GeminiContent content;
//        private String finishReason;
//    }
//
//    @Data
//    public static class GeminiUsageMetadata {
//        @JsonProperty("promptTokenCount")
//        private Integer promptTokenCount;
//        @JsonProperty("candidatesTokenCount")
//        private Integer candidatesTokenCount;
//        @JsonProperty("totalTokenCount")
//        private Integer totalTokenCount;
//    }
//}
