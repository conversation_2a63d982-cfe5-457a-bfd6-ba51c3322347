package com.wohoo_studio.backend.plumwhisperweb.ai.service;

import com.wohoo_studio.backend.plumwhisperweb.ai.client.FeishuApiClient;
import com.wohoo_studio.backend.plumwhisperweb.ai.model.dto.FeishuTableDataDTO;
import com.wohoo_studio.backend.plumwhisperweb.ai.model.enums.ModelType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 飞书服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class FeishuService {
    
    @Autowired
    private FeishuApiClient feishuApiClient;
    
    /**
     * 获取模型配置数据
     * 
     * @param modelType 模型类型
     * @return 配置数据
     */
    public FeishuTableDataDTO getModelConfig(ModelType modelType) {
        try {
            FeishuTableDataDTO config = feishuApiClient.getTableData(modelType.getCode());
            
            if (config == null) {
                log.warn("未找到模型 {} 的配置数据", modelType.getCode());
                return null;
            }
            
            if (!config.getEnabled()) {
                log.warn("模型 {} 已被禁用", modelType.getCode());
                return null;
            }
            
            if (config.getApiKeys() == null || config.getApiKeys().isEmpty()) {
                log.error("模型 {} 没有配置API Key", modelType.getCode());
                throw new RuntimeException("模型 " + modelType.getName() + " 没有配置API Key");
            }
            
            log.info("成功获取模型 {} 的配置，包含 {} 个API Key", 
                modelType.getCode(), config.getApiKeys().size());
            
            return config;
            
        } catch (Exception e) {
            log.error("获取模型配置失败: {}", modelType.getCode(), e);
            throw new RuntimeException("获取模型配置失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查模型是否可用
     * 
     * @param modelType 模型类型
     * @return 是否可用
     */
    public boolean isModelAvailable(ModelType modelType) {
        try {
            FeishuTableDataDTO config = getModelConfig(modelType);
            return config != null && config.getEnabled() && 
                   config.getApiKeys() != null && !config.getApiKeys().isEmpty();
        } catch (Exception e) {
            log.error("检查模型可用性失败: {}", modelType.getCode(), e);
            return false;
        }
    }
}
