package com.wohoo_studio.backend.plumwhisperweb.ai.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * API Key选择器工具类
 * 实现负载均衡和故障转移功能
 * <AUTHOR>
 */
@Slf4j
@Component
public class ApiKeySelector {
    
    private final SecureRandom random = new SecureRandom();
    
    /**
     * 记录每个API Key的使用次数
     */
    private final ConcurrentHashMap<String, AtomicInteger> usageCount = new ConcurrentHashMap<>();
    
    /**
     * 记录失败的API Key（简单的故障转移机制）
     */
    private final ConcurrentHashMap<String, Long> failedKeys = new ConcurrentHashMap<>();
    
    /**
     * 故障恢复时间（毫秒）
     */
    private static final long FAILURE_RECOVERY_TIME = 5 * 60 * 1000; // 5分钟
    
    /**
     * 随机选择一个可用的API Key
     * 
     * @param apiKeys API Key列表
     * @return 选中的API Key
     */
    public String selectApiKey(List<String> apiKeys) {
        if (apiKeys == null || apiKeys.isEmpty()) {
            throw new IllegalArgumentException("API Key列表不能为空");
        }
        
        // 过滤掉故障的API Key
        List<String> availableKeys = apiKeys.stream()
                .filter(this::isKeyAvailable)
                .toList();
        
        if (availableKeys.isEmpty()) {
            log.warn("所有API Key都处于故障状态，使用原始列表进行选择");
            availableKeys = apiKeys;
            // 清理故障记录，给API Key一个恢复的机会
            clearFailureRecords();
        }
        
        // 随机选择
        String selectedKey = availableKeys.get(random.nextInt(availableKeys.size()));
        
        // 记录使用次数
        usageCount.computeIfAbsent(selectedKey, k -> new AtomicInteger(0)).incrementAndGet();
        
        log.debug("选择API Key: {} (脱敏显示: {})", maskApiKey(selectedKey), maskApiKey(selectedKey));
        
        return selectedKey;
    }
    
    /**
     * 标记API Key为失败状态
     * 
     * @param apiKey 失败的API Key
     */
    public void markKeyAsFailed(String apiKey) {
        failedKeys.put(apiKey, System.currentTimeMillis());
        log.warn("标记API Key为失败状态: {}", maskApiKey(apiKey));
    }
    
    /**
     * 检查API Key是否可用
     * 
     * @param apiKey API Key
     * @return 是否可用
     */
    private boolean isKeyAvailable(String apiKey) {
        Long failureTime = failedKeys.get(apiKey);
        if (failureTime == null) {
            return true;
        }
        
        // 检查是否已经过了恢复时间
        if (System.currentTimeMillis() - failureTime > FAILURE_RECOVERY_TIME) {
            failedKeys.remove(apiKey);
            log.info("API Key恢复可用状态: {}", maskApiKey(apiKey));
            return true;
        }
        
        return false;
    }
    
    /**
     * 清理所有故障记录
     */
    private void clearFailureRecords() {
        failedKeys.clear();
        log.info("清理所有API Key故障记录");
    }
    
    /**
     * 获取API Key使用统计
     * 
     * @param apiKey API Key
     * @return 使用次数
     */
    public int getUsageCount(String apiKey) {
        AtomicInteger count = usageCount.get(apiKey);
        return count != null ? count.get() : 0;
    }
    
    /**
     * 脱敏显示API Key
     * 
     * @param apiKey 原始API Key
     * @return 脱敏后的API Key
     */
    public String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 8) {
            return "****";
        }
        
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }
    
    /**
     * 重置使用统计
     */
    public void resetUsageStats() {
        usageCount.clear();
        log.info("重置API Key使用统计");
    }
}
