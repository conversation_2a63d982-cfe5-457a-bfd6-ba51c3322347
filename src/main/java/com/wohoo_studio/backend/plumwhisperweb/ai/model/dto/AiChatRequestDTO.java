package com.wohoo_studio.backend.plumwhisperweb.ai.model.dto;

import com.wohoo_studio.backend.plumwhisperweb.ai.model.enums.ModelType;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * AI对话请求DTO
 * <AUTHOR>
 */
@Data
public class AiChatRequestDTO {
    
    /**
     * 模型类型
     */
    @NotNull(message = "模型类型不能为空")
    private ModelType modelType;
    
    /**
     * 用户输入内容
     */
    @NotBlank(message = "用户输入不能为空")
    private String userInput;
    
    /**
     * 会话ID（可选，用于上下文管理）
     */
    private String sessionId;
    
    /**
     * 系统提示词（可选，如果不提供则从飞书获取）
     */
    private String systemPrompt;
    
    /**
     * 温度参数（可选，控制回答的随机性）
     */
    private Double temperature;
    
    /**
     * 最大token数（可选）
     */
    private Integer maxTokens;
}
