//package com.wohoo_studio.backend.plumwhisperweb.ai.controller;
//
//import com.wohoo_studio.backend.plumwhisperweb.ai.model.dto.AiChatRequestDTO;
//import com.wohoo_studio.backend.plumwhisperweb.ai.model.enums.ModelType;
//import com.wohoo_studio.backend.plumwhisperweb.ai.model.vo.AiChatResponseVO;
//import com.wohoo_studio.backend.plumwhisperweb.ai.service.AiChatService;
//import com.wohoo_studio.backend.plumwhisperweb.common.Result;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import jakarta.validation.Valid;
//import java.util.Arrays;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * AI对话控制器
// * <AUTHOR>
// */
//@Slf4j
//@Validated
//@RestController
//@RequestMapping("/ai")
//public class AiChatController {
//
//    @Autowired
//    private AiChatService aiChatService;
//
//    /**
//     * AI对话接口
//     *
//     * @param request 对话请求
//     * @return 对话响应
//     */
//    @PostMapping("/chat")
//    public Result<AiChatResponseVO> chat(@Valid @RequestBody AiChatRequestDTO request) {
//        try {
//            log.info("收到AI对话请求: 模型={}, 用户输入长度={}",
//                request.getModelType().getCode(),
//                request.getUserInput() != null ? request.getUserInput().length() : 0);
//
//            AiChatResponseVO response = aiChatService.chat(request);
//
//            return Result.success(response);
//
//        } catch (Exception e) {
//            log.error("AI对话处理失败", e);
//            return Result.error("AI对话失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取支持的模型列表
//     *
//     * @return 模型列表
//     */
//    @GetMapping("/models")
//    public Result<List<Map<String, Object>>> getAvailableModels() {
//        try {
//            List<Map<String, Object>> models = Arrays.stream(ModelType.values())
//                    .map(modelType -> {
//                        boolean available = aiChatService.isModelAvailable(modelType);
//                        return Map.of(
//                            "code", modelType.getCode(),
//                            "name", modelType.getName(),
//                            "available", available
//                        );
//                    })
//                    .collect(Collectors.toList());
//
//            return Result.success(models);
//
//        } catch (Exception e) {
//            log.error("获取模型列表失败", e);
//            return Result.error("获取模型列表失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 检查特定模型是否可用
//     *
//     * @param modelCode 模型代码
//     * @return 可用性状态
//     */
//    @GetMapping("/models/{modelCode}/status")
//    public Result<Map<String, Object>> checkModelStatus(@PathVariable String modelCode) {
//        try {
//            ModelType modelType = ModelType.fromCode(modelCode);
//            boolean available = aiChatService.isModelAvailable(modelType);
//
//            Map<String, Object> status = Map.of(
//                "code", modelType.getCode(),
//                "name", modelType.getName(),
//                "available", available
//            );
//
//            return Result.success(status);
//
//        } catch (IllegalArgumentException e) {
//            return Result.error(400, "不支持的模型类型: " + modelCode);
//        } catch (Exception e) {
//            log.error("检查模型状态失败: {}", modelCode, e);
//            return Result.error("检查模型状态失败: " + e.getMessage());
//        }
//    }
//}
