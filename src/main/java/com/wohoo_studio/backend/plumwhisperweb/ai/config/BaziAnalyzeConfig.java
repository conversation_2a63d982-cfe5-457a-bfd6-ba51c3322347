package com.wohoo_studio.backend.plumwhisperweb.ai.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * AI相关配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "bazi.analyze")
public class BaziAnalyzeConfig {

    private FeishuConfig feishu = new FeishuConfig();

    private CacheConfig cache = new CacheConfig();

    @Data
    public static class FeishuConfig {

        private String baseUrl = "https://open.feishu.cn";

        private String appId;

        private String appSecret;

        private String tableId;

    }


    @Data
    public static class CacheConfig {

        private Boolean enabled = true;

        private Integer feishuCacheMinutes = 30;

    }

}
