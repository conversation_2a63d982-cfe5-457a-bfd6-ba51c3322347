package com.wohoo_studio.backend.plumwhisperweb.ai.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wohoo_studio.backend.plumwhisperweb.ai.client.base.BaseApiClient;
import com.wohoo_studio.backend.plumwhisperweb.ai.config.BaziAnalyzeConfig;
import com.wohoo_studio.backend.plumwhisperweb.ai.model.dto.FeishuTableDataDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 飞书API客户端
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FeishuApiClient extends BaseApiClient {

    @Autowired
    private BaziAnalyzeConfig baziAnalyzeConfig;

    private String accessToken;

    private long tokenExpireTime;

    /**
     * 获取访问令牌
     */
    public String getAccessToken() {
        // 如果token还没过期，直接返回
        if (accessToken != null && System.currentTimeMillis() < tokenExpireTime) {
            return accessToken;
        }

        try {
            String url = baziAnalyzeConfig.getFeishu().getBaseUrl() + "/open-apis/auth/v3/tenant_access_token/internal";

            Map<String, String> requestBody = Map.of(
                    "app_id", baziAnalyzeConfig.getFeishu().getAppId(),
                    "app_secret", baziAnalyzeConfig.getFeishu().getAppSecret()
            );

            Request request = new Request.Builder()
                    .url(url)
                    .post(createJsonRequestBody(requestBody))
                    .addHeader("Content-Type", "application/json")
                    .build();

            String response = executeRequest(request);
            TokenResponse tokenResponse = parseJsonResponse(response, TokenResponse.class);

            if (tokenResponse.getCode() != 0) {
                throw new RuntimeException("获取飞书访问令牌失败: " + tokenResponse.getMsg());
            }

            this.accessToken = tokenResponse.getTenantAccessToken();
            // 提前5分钟过期，避免边界情况
            this.tokenExpireTime = System.currentTimeMillis() + (tokenResponse.getExpire() - 300) * 1000L;

            log.info("成功获取飞书访问令牌，过期时间: {}", tokenExpireTime);
            return this.accessToken;

        } catch (Exception e) {
            log.error("获取飞书访问令牌失败", e);
            throw new RuntimeException("获取飞书访问令牌失败", e);
        }
    }

    /**
     * 从多维表格获取模型配置数据
     *
     * @param modelType 模型类型
     * @return 表格数据
     */
    @Cacheable(value = "feishuTableData", key = "#modelType", unless = "#result == null")
    public FeishuTableDataDTO getTableData() {
        try {
            String token = getAccessToken();
            String url = String.format("%s/open-apis/bitable/v1/apps/%s/tables/%s/records/search",
                    baziAnalyzeConfig.getFeishu().getBaseUrl(),
                    this.accessToken,
                    baziAnalyzeConfig.getFeishu().getTableId());

            // 构建查询条件
//            Map<String, Object> requestBody = Map.of(
//                    "filter", Map.of(
//                            "conditions", List.of(
//                                    Map.of(
//                                            "field_name", "model_type",
//                                            "operator", "is",
//                                            "value", List.of(modelType)
//                                    ),
//                                    Map.of(
//                                            "field_name", "enabled",
//                                            "operator", "is",
//                                            "value", List.of(true)
//                                    )
//                            ),
//                            "conjunction", "and"
//                    )
//            );

            Request request = new Request.Builder()
                    .url(url)
                    .post(createJsonRequestBody(requestBody))
                    .addHeader("Authorization", "Bearer " + token)
                    .addHeader("Content-Type", "application/json")
                    .build();

            String response = executeRequest(request);
            TableSearchResponse searchResponse = parseJsonResponse(response, TableSearchResponse.class);

            if (searchResponse.getCode() != 0) {
                throw new RuntimeException("查询飞书表格数据失败: " + searchResponse.getMsg());
            }

            if (searchResponse.getData().getItems().isEmpty()) {
                log.warn("未找到模型类型为 {} 的配置数据", modelType);
                return null;
            }

            // 取第一条记录
            Map<String, Object> record = searchResponse.getData().getItems().get(0).getFields();

            FeishuTableDataDTO result = new FeishuTableDataDTO();
//            result.setModelType(modelType);
            result.setSystemPrompt((String) record.get("system_prompt"));
            result.setModelConfig((String) record.get("model_config"));
            result.setEnabled((Boolean) record.getOrDefault("enabled", true));

            // 处理API Keys（假设是逗号分隔的字符串）
            String apiKeysStr = (String) record.get("api_keys");
            if (apiKeysStr != null && !apiKeysStr.trim().isEmpty()) {
                result.setApiKeys(Arrays.asList(apiKeysStr.split(",")));
            }

            log.info("成功获取模型 {} 的配置数据", modelType);
            return result;

        } catch (Exception e) {
            log.error("获取飞书表格数据失败: modelType={}", modelType, e);
            throw new RuntimeException("获取飞书表格数据失败", e);
        }
    }

    @Data
    private static class TokenResponse {
        private int code;
        private String msg;
        @JsonProperty("tenant_access_token")
        private String tenantAccessToken;
        private int expire;
    }

    @Data
    private static class TableSearchResponse {
        private int code;
        private String msg;
        private TableData data;
    }

    @Data
    private static class TableData {
        private List<TableRecord> items;
    }

    @Data
    private static class TableRecord {
        private Map<String, Object> fields;
    }
}
