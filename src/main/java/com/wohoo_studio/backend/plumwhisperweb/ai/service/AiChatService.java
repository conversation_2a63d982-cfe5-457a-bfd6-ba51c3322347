//package com.wohoo_studio.backend.plumwhisperweb.ai.service;
//
//import com.wohoo_studio.backend.plumwhisperweb.ai.client.GeminiApiClient;
//import com.wohoo_studio.backend.plumwhisperweb.ai.model.dto.AiChatRequestDTO;
//import com.wohoo_studio.backend.plumwhisperweb.ai.model.dto.FeishuTableDataDTO;
//import com.wohoo_studio.backend.plumwhisperweb.ai.model.enums.ModelType;
//import com.wohoo_studio.backend.plumwhisperweb.ai.model.vo.AiChatResponseVO;
//import com.wohoo_studio.backend.plumwhisperweb.ai.util.ApiKeySelector;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.time.LocalDateTime;
//import java.util.UUID;
//
///**
// * AI对话服务
// * <AUTHOR>
// */
//@Slf4j
//@Service
//public class AiChatService {
//
//    @Autowired
//    private FeishuService feishuService;
//
//    @Autowired
//    private GeminiApiClient geminiApiClient;
//
//    @Autowired
//    private ApiKeySelector apiKeySelector;
//
//    /**
//     * 进行AI对话
//     *
//     * @param request 对话请求
//     * @return 对话响应
//     */
//    public AiChatResponseVO chat(AiChatRequestDTO request) {
//        long startTime = System.currentTimeMillis();
//
//        try {
//            // 1. 获取模型配置
//            FeishuTableDataDTO modelConfig = feishuService.getModelConfig(request.getModelType());
//            if (modelConfig == null) {
//                throw new RuntimeException("模型 " + request.getModelType().getName() + " 不可用");
//            }
//
//            // 2. 选择API Key
//            String selectedApiKey = apiKeySelector.selectApiKey(modelConfig.getApiKeys());
//
//            // 3. 确定系统提示词
//            String systemPrompt = request.getSystemPrompt() != null ?
//                request.getSystemPrompt() : modelConfig.getSystemPrompt();
//
//            // 4. 根据模型类型调用相应的API
//            AiChatResponseVO response = callModelApi(
//                request.getModelType(),
//                selectedApiKey,
//                systemPrompt,
//                request.getUserInput(),
//                request.getTemperature(),
//                request.getMaxTokens()
//            );
//
//            // 5. 设置响应信息
//            response.setSessionId(request.getSessionId() != null ?
//                request.getSessionId() : UUID.randomUUID().toString());
//            response.setModelType(request.getModelType());
//            response.setResponseTime(System.currentTimeMillis() - startTime);
//            response.setCreateTime(LocalDateTime.now());
//            response.setApiKeyUsed(apiKeySelector.maskApiKey(selectedApiKey));
//
//            log.info("AI对话完成: 模型={}, 耗时={}ms, token={}",
//                request.getModelType().getCode(), response.getResponseTime(), response.getTokensUsed());
//
//            return response;
//
//        } catch (Exception e) {
//            log.error("AI对话失败: 模型={}, 错误={}", request.getModelType().getCode(), e.getMessage(), e);
//            throw new RuntimeException("AI对话失败: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 调用具体的模型API
//     */
//    private AiChatResponseVO callModelApi(ModelType modelType, String apiKey, String systemPrompt,
//                                         String userInput, Double temperature, Integer maxTokens) {
//
//        AiChatResponseVO response = new AiChatResponseVO();
//
//        try {
//            switch (modelType) {
//                case GEMINI -> {
//                    GeminiApiClient.GeminiResponse geminiResponse = geminiApiClient.chat(
//                        apiKey, systemPrompt, userInput, temperature, maxTokens);
//
//                    if (geminiResponse.getCandidates() == null || geminiResponse.getCandidates().isEmpty()) {
//                        throw new RuntimeException("Gemini返回空响应");
//                    }
//
//                    GeminiApiClient.GeminiCandidate candidate = geminiResponse.getCandidates().get(0);
//                    if (candidate.getContent() == null || candidate.getContent().getParts() == null ||
//                        candidate.getContent().getParts().isEmpty()) {
//                        throw new RuntimeException("Gemini返回无效内容");
//                    }
//
//                    response.setContent(candidate.getContent().getParts().get(0).getText());
//                    response.setTokensUsed(geminiResponse.getUsageMetadata() != null ?
//                        geminiResponse.getUsageMetadata().getTotalTokenCount() : 0);
//                }
//
//                case CHATGPT, CLAUDE, QWEN, BAIDU -> {
//                    // TODO: 实现其他模型的API调用
//                    throw new RuntimeException("模型 " + modelType.getName() + " 暂未实现");
//                }
//
//                default -> throw new RuntimeException("不支持的模型类型: " + modelType.getCode());
//            }
//
//        } catch (Exception e) {
//            // 标记API Key为失败状态
//            apiKeySelector.markKeyAsFailed(apiKey);
//            throw e;
//        }
//
//        return response;
//    }
//
//    /**
//     * 检查模型是否可用
//     *
//     * @param modelType 模型类型
//     * @return 是否可用
//     */
//    public boolean isModelAvailable(ModelType modelType) {
//        return feishuService.isModelAvailable(modelType);
//    }
//}
