package com.wohoo_studio.backend.plumwhisperweb.ai.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AI模型类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ModelType {
    
    GEMINI("gemini", "Google Gemini"),
    CHATGPT("chatgpt", "OpenAI ChatGPT"),
    CLAUDE("claude", "Anthropic Claude"),
    QWEN("qwen", "阿里通义千问"),
    BAIDU("baidu", "百度文心一言");
    
    private final String code;
    private final String name;
    
    /**
     * 根据代码获取模型类型
     */
    public static ModelType fromCode(String code) {
        for (ModelType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown model type: " + code);
    }
}
