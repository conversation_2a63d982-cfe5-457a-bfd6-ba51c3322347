package com.wohoo_studio.backend.plumwhisperweb.ai.model.vo;

import com.wohoo_studio.backend.plumwhisperweb.ai.model.enums.ModelType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * AI对话响应VO
 * <AUTHOR>
 */
@Data
public class AiChatResponseVO {
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 使用的模型类型
     */
    private ModelType modelType;
    
    /**
     * AI回复内容
     */
    private String content;
    
    /**
     * 使用的token数量
     */
    private Integer tokensUsed;
    
    /**
     * 响应时间（毫秒）
     */
    private Long responseTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 使用的API Key（脱敏显示）
     */
    private String apiKeyUsed;
}
