package com.wohoo_studio.backend.plumwhisperweb.ai.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 飞书多维表格数据DTO
 * <AUTHOR>
 */
@Data
public class FeishuTableDataDTO {
    
    /**
     * 模型类型
     */
    private String modelType;
    
    /**
     * 系统提示词
     */
    private String systemPrompt;
    
    /**
     * API Key数组
     */
    private List<String> apiKeys;
    
    /**
     * 模型配置（JSON格式）
     */
    private String modelConfig;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
}
