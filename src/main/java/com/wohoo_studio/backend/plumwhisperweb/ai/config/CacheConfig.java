package com.wohoo_studio.backend.plumwhisperweb.ai.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.Set;

/**
 * 缓存配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableCaching
public class CacheConfig {


    @Bean
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        Set<String> feishuTableData = Collections.singleton("feishuTableData");
        cacheManager.setCacheNames(feishuTableData);
        return cacheManager;
    }
}
