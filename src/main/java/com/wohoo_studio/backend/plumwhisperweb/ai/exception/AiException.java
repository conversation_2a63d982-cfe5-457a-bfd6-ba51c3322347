package com.wohoo_studio.backend.plumwhisperweb.ai.exception;

/**
 * AI相关异常
 * <AUTHOR>
 */
public class AiException extends RuntimeException {
    
    private final String errorCode;
    
    public AiException(String message) {
        super(message);
        this.errorCode = "AI_ERROR";
    }
    
    public AiException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public AiException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "AI_ERROR";
    }
    
    public AiException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}
