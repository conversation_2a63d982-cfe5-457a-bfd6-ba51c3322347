package com.wohoo_studio.backend.plumwhisperweb;

import com.wohoo_studio.backend.plumwhisperweb.ai.client.FeishuApiClient;
import com.wohoo_studio.backend.plumwhisperweb.ai.model.dto.FeishuTableDataDTO;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class PlumWhisperWebApplicationTests {

    private static final Logger log = LoggerFactory.getLogger(PlumWhisperWebApplicationTests.class);

    @Autowired
    private FeishuApiClient feishuApiClient;

    @Test
    void feishuApiClientGetAccessTokenTest() {
        String accessToken = feishuApiClient.getAccessToken();
        log.info("feishuApiClientTest, accessToken:{}", accessToken);
    }

    @Test
    void feishuApiClientGetTableDataTest() {
        FeishuTableDataDTO tableData = feishuApiClient.getTableData();
        log.info("feishuApiClientTest, tableData:{}", tableData);
    }

}
